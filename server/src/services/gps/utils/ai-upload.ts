import {HookContext} from '../../../declarations.js';
import OpenAi from 'openai';
import {fixedRate} from '../../coverages/schemas/rates.js';
import {coverageCalcSchema} from '../../coverages/schemas/benefits.js';
import {dollarString} from '../../../utils/simple.js';
import {CoreCall} from 'feathers-ucan';
import {File, FormData} from 'formdata-node'
import axios from 'axios';
import {FormDataEncoder} from 'form-data-encoder';
import {Readable} from 'stream';
import {getCoverageRate} from '../../enrollments/utils/index.js';
import {getFileSchema} from '../../uploads/utils/index.js';

export const aiUpload = async (context: HookContext) => {
    const {ai_upload} = context.params.runJoin || {}
    if (ai_upload) {
        if (context.type === 'before') {
            context.data = {updatedAt: new Date()}
            return context;
        }
        const {files = []} = context.params;
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const {employerContribution} = context.result;

        const fName = files[0].name;
        const b = files[0].buffer;
        const mimeType = files[0].mimetype || 'application/pdf'; // fallback
        const f = new File([b], fName || 'benefits_docs.pdf', {type: mimeType});
        const frm = new FormData()
        frm.append('file', f)
        frm.append('purpose', 'assistants')
        // frm.set('type', f.type)
        // frm.set('mimetype', f.type)

        const encoder = new FormDataEncoder(frm as any);

        const docsRes: any = await axios.post(
            'https://api.openai.com/v1/files',
            Readable.from(encoder.encode()),
            {
                headers: {
                    'Authorization': `Bearer ${key}`,
                    ...encoder.headers
                }
            }).catch(err => {
            console.log(`Error uploading benefit docs: ${err.message}`)
            throw new Error(`Error uploading benefit docs: ${err.message}`)
        })

        const docsFile = docsRes.data;

        let er = '';
        if (employerContribution?.amount) {
            const args: any[] = employerContribution.type === 'percent' ? ['', 2, '%'] : ['$', 2, '']
            er = `unless explicitly listed, the rates in these documents don\'t account for employer contributions. The employer contribution you should use by default are ${dollarString(employerContribution.amount, args[0], args[1])}${args[2]} for individuals${employerContribution.family ? ` and ${dollarString(employerContribution.family, args[0], args[1]) + ` ${args[2]} for families`}` : ''}. Please adjust the rates to account for the employer contribution - so the coverage data object has the actual total cost, not just the employee portion.`;

        }

        const schemaProperties = ['carrierName', 'webpage', 'name', 'plan_type', 'type', 'hsaQualified', 'maxAge', 'coinsurance', 'deductible', 'copays', 'benefits', 'moop']
        const properties = {
            premium: {
                $comment: 'Add only known ages and fixed rate keys (household key, single, plus_one, etc',
                type: 'object',
                patternProperties: {
                    "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {
                        type: 'object',
                        $comment: 'This key is the age of the participant - or oldest age in the household',
                        properties: fixedRate.properties
                    },

                }
            }
        };
        for (const k of schemaProperties) {
            properties[k] = coverageCalcSchema.properties[k]
        }
        const schema = {
            type: 'object', properties
        }

        const result = await openai.chat.completions.create({
                model: 'gpt-4o',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an assistant helping to take images of health plan benefit documents and parse them so they are valid JSON data'
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: `Please extract the employer health plan benefit details from the document attached under file_ids, and structure the output as a JSON object that conforms to the schema provided in response_format. Refer to the $comment fields in the schema for guidance on each property's meaning and expected format. The format allows for up to 4 different plans if there are multiple plans detailed in the documents. You can see they are keyed as "1","2","3","4".
                               

⚠️ Pay special attention to how the employee premium payment or deduction amount is expressed. It may be listed "per pay period"—which could mean **bi-weekly** or **semi-monthly**. Always normalize this amount to a **monthly** equivalent in the final JSON output. It will also often show employee/employer contributions separately. We are looking for the total cost/premium only. Only provide values for the ages and household keys that are explicitly listed. I will have separate tooling to fill in the blanks. Omit blanks, don't add null or undefined values.

If the document is an invoice, realize that often participant premiums are totaled - we need the total per-participant by age and household size, not a combined total. 

Do your best to infer deductibles, copays, and moop from the documents or plan name/details.

Be certain you key the premiums by age. Sometimes you won't know an age, and if that's the case guess age 40. Follow the json schema for premiums. Here it is again in typescript - { {[age:string]: { single: number, plus_spouse: number, plus_child: number, plus_child__2: number, plus_child__3: number, family: number }}]

${er}`

                            },
                            {
                                type: 'file',
                                file: {
                                    file_id: docsFile.id
                                }
                            }
                        ]
                    }
                ],
                response_format: {
                    type: 'json_schema',
                    json_schema: {
                        name: 'coverages', schema: {
                            $comment: 'key 1,2,3,4 for the number of coverages found in the document. Maximum of 4 coverages per document',
                            additionalProperties: true,
                            type: 'object', properties: {
                                "1": schema,
                                "2": schema,
                                "3": schema,
                                "4": schema,
                            }
                        }
                    }
                }
            }
        )
            .catch(err => {
                console.log(`Error uploading and scanning plan documents: ${err.message}`)
                throw new Error(`Error processing plan documents: ${err.message}`)
            })

        const res = result ? JSON.parse((result.choices || [])[0]?.message?.content?.replace('```json', '').replace('```', '')?.trim().replace(/[']/g, '') || "{}") : {};

        const coverages:any = {};
        if(Object.keys(res).length) {
            const {name, size, type, mimetype, lastModifiedDate, originalname} = files[0];
            const info = {name: name || originalname, size, type: type || mimetype, lastModifiedDate};
            for (const k in info) {
                if (!info[k]) delete info[k];
            }
            const data: any = {info, file: files[0], storage: 'storj'}
            if (originalname) data.originalname = originalname
            const upload = await new CoreCall('uploads', context).create(data, {
                allow_upload: true,
                admin_pass: true,
                file: files[0]
            })
                .catch(err => {
                    console.log(`Failed to upload file in gps ai-upload: ${err.message}`)
                })
            const fileSchema = getFileSchema(upload || {})
            const patchObj:any = { $set: {} }
            for (let i = 0; i < Object.keys(res).length; i++) {
                const opt = res[Object.keys(res)[i]];
                opt.premium = { fixedRates: opt.premium, rateType: 'fixedRates' }
                const premium = getCoverageRate({coverage: opt, enrolled: [{age: 40, relation: 'self'}]})
                if (!premium) throw new Error('Could not calculate premium from ai response')
                opt.fortyPremium = premium;
                opt.id = new Date().getTime().toString() + `-${i}`;
                opt.fromFile = true;
                opt.files = [fileSchema]
                coverages[opt.id] = opt
                patchObj.$set[`coverages.${opt.id}`] = opt
            }

            await new CoreCall('gps', context).patch(context.result._id, patchObj, {admin_pass: true})
            context.result.coverages = {...context.result.coverages, ...coverages}
        }
        return context;
    }
    return context;
}
